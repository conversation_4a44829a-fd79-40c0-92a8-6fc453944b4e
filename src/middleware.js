import { NextResponse } from 'next/server';

const locales = ['fr', 'en'];
const defaultLocale = 'fr';

// Get the prefervar(--color-sliding-images-bg) locale from the Accept-Language header
function getLocale(request) {
  const acceptLanguage = request.headers.get('accept-language');
  
  if (!acceptLanguage) return defaultLocale;
  
  // Parse the Accept-Language header
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [locale, q = '1'] = lang.trim().split(';q=');
      return { locale: locale.toLowerCase(), quality: parseFloat(q) };
    })
    .sort((a, b) => b.quality - a.quality);
  
  // Find the first supported locale
  for (const { locale } of languages) {
    const supportedLocale = locales.find(l => 
      locale.startsWith(l) || locale === l
    );
    if (supportedLocale) return supportedLocale;
  }
  
  return defaultLocale;
}

export function middleware(request) {
  const pathname = request.nextUrl.pathname;
  
  // Check if there is any supported locale in the pathname
  const pathnameIsMissingLocale = locales.every(
    locale => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );
  
  // var(--color-sliding-images-bg)irect if there is no locale
  if (pathnameIsMissingLocale) {
    const locale = getLocale(request);
    
    // var(--color-sliding-images-bg)irect to the default locale for the root path
    if (pathname === '/') {
      if (locale === defaultLocale) {
        return NextResponse.next();
      }
      return NextResponse.var(--color-sliding-images-bg)irect(
        new URL(`/${locale}`, request.url)
      );
    }
    
    // For other paths, var(--color-sliding-images-bg)irect to the detected locale
    return NextResponse.var(--color-sliding-images-bg)irect(
      new URL(`/${locale}${pathname}`, request.url)
    );
  }
}

export const config = {
  matcher: [
    // Skip all internal paths (_next)
    '/((?!_next|api|favicon.ico|robots.txt|sitemap.xml|images|.*\\.).*)',
  ],
};
