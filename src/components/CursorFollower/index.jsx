import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import styles from './style.module.scss';

const scaleAnimation = {
  initial: { scale: 0, x: "-50%", y: "-50%" },
  enter: { scale: 1, x: "-50%", y: "-50%", transition: { duration: 0.4, ease: [0.76, 0, 0.24, 1] } },
  closed: { scale: 0, x: "-50%", y: "-50%", transition: { duration: 0.4, ease: [0.32, 0, 0.67, 0] } }
};

const CursorFollower = forwardRef(function CursorFollower({
  text = "Explorer",
  backgroundColor = "var(--color-secondary)",
  textColor = "var(--color-text-white)",
  size = 80,
  fontSize = 14,
  fontWeight = 300,
  isVisible = false,
  className = ""
}, ref) {
  const cursorRef = useRef(null);
  const xMoveCursor = useRef(null);
  const yMoveCursor = useRef(null);

  useEffect(() => {
    if (cursorRef.current) {
      xMoveCursor.current = gsap.quickTo(cursorRef.current, "left", {
        duration: 0.5,
        ease: "power3"
      });
      yMoveCursor.current = gsap.quickTo(cursorRef.current, "top", {
        duration: 0.5,
        ease: "power3"
      });
    }
  }, []);

  // Fonction pour mettre à jour la position du curseur
  const updatePosition = (x, y) => {
    if (xMoveCursor.current && yMoveCursor.current) {
      xMoveCursor.current(x);
      yMoveCursor.current(y);
    }
  };

  // Exposer la fonction updatePosition via une ref
  useImperativeHandle(ref, () => ({
    updatePosition
  }));

  const cursorStyle = {
    width: `${size}px`,
    height: `${size}px`,
    backgroundColor,
    color: textColor,
    fontSize: `${fontSize}px`,
    fontWeight
  };

  return (
    <motion.div
      ref={cursorRef}
      className={`${styles.cursorFollower} ${className}`}
      style={cursorStyle}
      variants={scaleAnimation}
      initial="initial"
      animate={isVisible ? "enter" : "closed"}
    >
      {text}
    </motion.div>
  );
});

export default CursorFollower;

// Hook personnalisé pour faciliter l'utilisation
export function useCursorFollower() {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const cursorRef = useRef(null);

  const showCursor = (x, y) => {
    setPosition({ x, y });
    setIsVisible(true);
    if (cursorRef.current && cursorRef.current.updatePosition) {
      cursorRef.current.updatePosition(x, y);
    }
  };

  const hideCursor = () => {
    setIsVisible(false);
  };

  const updatePosition = (x, y) => {
    setPosition({ x, y });
    if (cursorRef.current && cursorRef.current.updatePosition) {
      cursorRef.current.updatePosition(x, y);
    }
  };

  return {
    isVisible,
    position,
    showCursor,
    hideCursor,
    updatePosition,
    cursorRef
  };
}
