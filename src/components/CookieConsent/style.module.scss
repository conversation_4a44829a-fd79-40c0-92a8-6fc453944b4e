.cookieConsentWrapper {
    position: fixed;
    bottom: 2rem;
    width: 100%;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 10;
  }
  
  
  .cookieConsentContainer {
    max-width: 600px; // ajuster la largeur max pour desktop
    padding-left: var(--container-padding);
    padding-right: var(--container-padding);
  }
  
  .cookieConsent {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--color-primary);
    border-radius: 100px;
    padding: var(--gap-padding) calc(var(--gap-padding) * 2);
    color: var(--color-background-primary);
  }
  
  .cookieConsentText {
    margin-bottom: calc(var(--gap-padding) / 2);
  }
  
  .cookieConsentLinks {
    display: flex;
    gap: 1rem;
  }
  

  @media (min-width: 768px) {
    .cookieConsent {
        flex-direction: row;
        justify-content: space-between;
        padding: calc(var(--gap-padding) / 2)  var(--gap-padding);
        gap: 3rem;


        .cookieConsentText {
            margin-bottom: 0;
        }
    }
  }