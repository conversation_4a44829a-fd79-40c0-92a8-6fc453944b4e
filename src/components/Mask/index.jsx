// components/Mask.js
import React from 'react';
import styles from './style.module.scss';

export default function Mask({ invert = false, color = "var(--color-primary)", overlay = false, wrap = false, hoverAnim = false, children, disableMobile = false}) {
  const style = {
    backgroundColor: color,
    transform: invert ? "scale(-1, -1)" : "none",
    transformOrigin: invert ? "top left" : undefined,
  };

  if (wrap) {
    return (
<div
       className={[
         styles.maskWrap,
         hoverAnim   && styles.hoverAnim,
         invert      && styles.invert,
         disableMobile && styles.noMobileMask,  // ← applique la classe
       ]
         .filter(Boolean)
         .join(" ")
       }
       style={style}
         >
      { children }
      </div>
    );
  }

  return <div className={`${styles.mask} ${overlay ? styles.overlay : ''}`} style={style} />;
}
