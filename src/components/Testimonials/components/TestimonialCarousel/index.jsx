'use client';
import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './style.module.scss';
import CursorFollower, { useCursorFollower } from '@/components/CursorFollower';
import { useTranslation } from '@/hooks/useTranslation';

export default function TestimonialCarousel({ testimonials, locale = 'fr', dragText = 'testimonials.drag_text' }) {
  const { t } = useTranslation('agency');
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    dragFree: true,
    containScroll: 'trimSnaps'
  });
  const [scrollProgress, setScrollProgress] = useState(0);

  // Hook pour gérer la bulle qui suit la souris
  const { isVisible, showCursor, hideCursor, updatePosition, cursorRef } = useCursorFollower();

  const onScroll = useCallback((emblaApi) => {
    const progress = Math.max(0, Math.min(1, emblaApi.scrollProgress()));
    setScrollProgress(progress);
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onScroll(emblaApi);
    emblaApi.on('reInit', onScroll);
    emblaApi.on('scroll', onScroll);
    emblaApi.on('slideFocus', onScroll);
  }, [emblaApi, onScroll]);

  // Gestionnaires d'événements pour le cursor follower
  const handleMouseEnter = (e) => {
    showCursor(e.clientX, e.clientY);
  };

  const handleMouseMove = (e) => {
    updatePosition(e.clientX, e.clientY);
  };

  const handleMouseLeave = () => {
    hideCursor();
  };

  return (
    <div className={styles.carousel}>
      {/* Embla Carousel Container */}
      <div
        className={styles.embla}
        ref={emblaRef}
        onMouseEnter={handleMouseEnter}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        <div className={styles.emblaContainer}>
          {testimonials.map((testimonial, index) => (
            <div key={index} className={styles.emblaSlide}>
              <div className={styles.testimonialCard}>
                  {testimonial.content}
                <p className={styles.author}>
                  {testimonial.name}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Indicateur de progression en ligne */}
      <div className={styles.progressIndicator}>
        <div
          className={styles.progressBar}
          style={{ width: `${scrollProgress * 100}%` }}
        />
      </div>

      {/* Bulle qui suit la souris */}
      <CursorFollower
        ref={cursorRef}
        text={t(dragText)}
        backgroundColor="var(--color-secondary)"
        textColor="var(--color-text-white)"
        size={80}
        fontSize={14}
        fontWeight={300}
        isVisible={isVisible}
      />
    </div>
  );
}
