// .filterContainer {
//     // display: flex;
//     // flex-direction: column;
//     // align-items: center;
//     // padding: 20px;
//     // box-sizing: border-box;
//     width: 100%;

.filterRow {
  align-items: center;
  display: flex;
  background-color: var(--color-background-primary);
  //   flex-wrap: wrap;
  justify-content: space-between;
  //   width: 100%;
  margin-bottom: 20px;
  overflow-x: auto;
  /* Permet le défilement horizontal */
  -webkit-overflow-scrolling: touch;
  /* Pour un défilement fluide sur iOS */
  scrollbar-width: none;
  /* Cache la barre de défilement (Firefox) */

  &::-webkit-scrollbar {
    display: none;
    /* Cache la barre de défilement (Chrome, Safari, Edge) */
  }

  /* Styles spécifiques pour desktop */
  @media (min-width: 1200px) {
    /* Ajuste la résolution selon tes besoins */
    overflow-x: visible;
    /* Retire le défilement horizontal */
    -webkit-overflow-scrolling: auto;
    /* Optionnel : annule le comportement mobile */
  }

  .toggleRow {
    display: flex;
    gap: 15px;

    button {
      position: relative;
      padding: 0;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover .bg {
        background-color: rgb(193, 2, 6);
      }

      .bg {
        background-color: rgb(28, 29, 32);
        width: 150%;
        height: 136px;
        border-radius: 50%;
        position: absolute;
        top: -34px;
        left: -24px;
        transition: background-color 0.25s ease-in-out;
      }

      p {
        z-index: 2;
        color: var(--color-text-white);
        font-size: 16px;
        font-weight: 450;
        padding: 0 40px;
        height: 68px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }

      .activeFilter {
        color: rgb(255, 255, 255);
      }
    }
  }

  .viewModeRow {
    display: none;
    gap: 15px;

    @media (min-width: 1200px) {
      display: flex;
    }


  }

  .svgFilter {
    width: 16px;
    height: 16px;
    fill: currentColor;
    display: block;
    /* Pour éviter les espaces en bas */
  }

  /* Styles spécifiques pour les boutons de vue */
  .viewModeButton {
    :global(.content) {
      padding: 0 !important;
      width: 100%;
      height: 100%;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    p {
      margin: 0 !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 100% !important;
      height: 100% !important;
    }
  }
}

//   }


.listView {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.gridView {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}