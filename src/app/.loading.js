// src/app/loading.js
'use client'
import { motion, AnimatePresence } from 'framer-motion'
import { stairsVariants } from '../components/Stairs/anim'  // assure-toi que ce chemin est correct

export default function Loading() {
  const nbOfColumns = 3

  return (
    <AnimatePresence>
      <motion.div
        key="loading-container"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 9999,
          pointerEvents: 'none',
        }}
      >
        {/* Couche de fond : apparaît en milieu d'animation */}
        <motion.div
          key="background-layer"
          initial={{ opacity: 0 }}
          animate={{ 
            opacity: 1, 
            transition: { delay: 0.5, duration: 0.3 } // ajuste ces valeurs pour le timing voulu
          }}
          exit={{ opacity: 0, transition: { duration: 0.3 } }}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'var(--color-primary)',
            zIndex: 1,
          }}
        />

        {/* Conteneur des colonnes ("stairs") */}
        <div
          style={{
            position: 'fixed',
            display: 'flex',
            width: '100vw',
            height: '100vh',
            top: 0,
            left: 0,
            zIndex: 2,
          }}
        >
          {[...Array(nbOfColumns)].map((_, i) => (
            <motion.div
              key={i}
              custom={i}
              initial="initial"
              animate="cover"
              exit="uncover"
              variants={stairsVariants}
              style={{
                flex: 1,
                backgroundColor: 'var(--color-primary)',
              }}
            />
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
