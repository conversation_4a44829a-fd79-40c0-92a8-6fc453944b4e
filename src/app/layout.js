// layout.js
import './globals.scss';
import { Plus_Jakarta_Sans } from 'next/font/google';
import GoogleAnalytics from './GoogleAnalytics';
import Header from '@/components/Header';
import StairsLayout from '@/components/Stairs';
import SelectionColorChanger from '@/common/Selection';
import CookieConsent from '@/components/CookieConsent';
import Footer from '@/components/Footer';
import CanonicalHead from './CanonicalHead';
import Head from 'next/head';
import LenisScrollProvider from '@/components/LocomotiveScrollProvider';
import 'material-symbols';

const plusJakartaSans = Plus_Jakarta_Sans({ subsets: ['latin'] });

const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Kapreon",
  "url": "https://kapreon.com",
  "logo": "https://kapreon.com/logo.png"
};
export const metadata = {
  metadataBase: new URL('https://kapreon.com'),
  title: 'Kapreon | Agence créative design et web',
  description: 'Kapreon, agence créative à Montréal, spécialisée en développement web sur mesure, design UX/UI et identités visuelles pour renforcer votre présence en ligne.',
  verification: {
    google: '1XC-nn6NtCPE3WayReHxS2iSwBKwDk8o79nqknbwDMU',
  },
};


export default function RootLayout({ children }) {
  // This layout is only for the root var(--color-sliding-images-bg)irect
  return (
    <html lang="fr">
      <body className={plusJakartaSans.className}>
        {children}
      </body>
    </html>
  );
}