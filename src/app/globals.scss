@import "../styles/typography.scss";
@import "../styles/variables.scss";
@import "material-symbols";


.material-symbols-outlined {
  font-variation-settings:
  'FILL' 0,
  'wght' 200,
  'GRAD' 0,
  'opsz' 40;
  font-size: 40px;
}

::selection {
  background: var(--selection-color, #ff0000);
  color: #fff;
}



body {
  margin: 0px;
  // overflow: hidden; /* Hide scrollbars */
  cursor: wait;
  background: #fff;
}

main {
  background: #FCFCF8;
}

:root {
  --container-padding: clamp(2.5em, 8vw, 8em);
  --section-padding: clamp(5em, 21vh, 12em);
  --gap-padding: clamp(1.5em, 4vw, 2.5em);
  --section-padding: clamp(5em, 21vh, 12em);
}

@media screen and (max-width: 540px){

  :root { 
      --container-padding: clamp(1.25em, 4vw, 2.5em);
      --section-padding: max(2.5em, 12vh);
  }

  .container.small {
    padding-left: var(--container-padding) !important;
    padding-right: var(--container-padding) !important;
  }
}

.section {
  padding-top: var(--section-padding);
  padding-bottom: var(--section-padding);
} 

.container {
  margin: 0 auto;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
  max-width: 100em;
}

.container.large {
  padding-left: var(--gap-padding);
  padding-right: var(--gap-padding);
}

.container.medium {
  padding-left: calc(var(--container-padding) * 2);
  padding-right: calc(var(--container-padding) * 2);
}

.container.small {
  padding-left: calc(var(--container-padding) * 4);
  padding-right: calc(var(--container-padding) * 4);
}

.container.no-padding {
  padding-left: unset;
  padding-right: unset;
}

.container.left {
  padding-left: var(--container-padding);
  padding-right: calc(var(--container-padding) * 6);
}


.default-hero {
  padding-top: calc(var(--section-padding)* 1.33);
  padding-bottom: calc(var(--section-padding)* .66);
  background: #FCFCF8;
}











// Accordion

// * {
//   padding: 0;
//   margin: 0;
//   box-sizing: border-box;
// }

// body {
//   background-color: #f2f2f2;
// }

// .container {
//   max-width: 650px;
//   width: 100%;
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
// }

.wrapper {
  border-bottom: 1px solid black;
  overflow: hidden;
}

.wrapper .question-container {
  width: 100%;
  text-align: left;
  padding: 20px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.question-container.active {
  color: #1db954;
  background-image: linear-gradient(90deg,transparent,rgba(0,0,0,0.04),transparent);
}

.wrapper .question-container:hover {
  background-image: linear-gradient(90deg,transparent,rgba(0,0,0,0.04),transparent);
}

.wrapper .arrow {
  font-size: 2rem;
  transition: .5s ease-in-out;
}

.arrow.active {
  rotate: 180deg;
  color: #1db954;
}

.wrapper .answer-container {
  padding: 0 1rem;
  transition: height .7s ease-in-out;
}

.wrapper .answer-content {
  padding: 1rem 0;
  font-size: 20px;
  font-style: italic;
}