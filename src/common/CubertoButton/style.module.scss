
.buttonWrapper {
    display: flex;
    // justify-content: center;
    // align-items: center;
    // min-height: 100vh;
  }
  .btnLink {
    display: inline;
  }
  .btnLinkCta {
    position: relative;
    display: inline-block;
    height: auto;
    margin: 0;
    padding: 0;
    color: inherit;
    background: 0 0;
    border: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    letter-spacing: inherit;
    line-height: inherit;
    text-align: inherit;
    text-transform: inherit;
    text-indent: inherit;
    text-decoration: none;
  }
  .btnLinkCta:hover,
  .btnLinkCta:focus {
    text-decoration: none;
  }
  .btnLinkCta {
    overflow: hidden;
    padding: 1.8rem;
    transform: translateZ(0);
    border-radius: 1000px;
    font-weight: 500;
    // font-size: 1.4rem;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform 0.3s;
  }
  .btnLinkCta {
    // padding: 0 1.5rem;
    font-weight: 400;
    // font-size: 105%;
    line-height: 0.92;
    // height: 6rem;
  }
  .btnLinkCta.xxl {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 500px;
    // height: 14rem;
    font-weight: 500;
    // font-size: 2rem;
    text-transform: none;
  }
  .btnBorder {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  .btnRipple {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  .btnRipple span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background-color: #031026;
    border-radius: 50% 50% 0 0;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0, 1),
      border-radius 0.5s cubic-bezier(0.4, 0, 0, 1);
  }
  // .cb-btn_cta-title {
  //   position: relative;
  //   display: block;
  //   padding: 0 0.16em 0 0;
  //   overflow: hidden;
  //   z-index: 2;
  // }
  .btnTitle {
    position: relative;
    display: block;
    padding: 0 0.16em 0 0;
    overflow: hidden;
    z-index: 2;
  }
  .btnTitle span {
    display: block;
    transition: transform 0.8s cubic-bezier(0.16, 1, 0.3, 1);
    padding: 5px;
  }
  .btnTitle span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 125%;
    left: 5px;
    color: var(--color-text-var(--color-text-white));
  }
  
  @media (pointer: fine) {
    .btnLinkCta:hover .btnTitle span {
      transform: translateY(-110%);
    }
  }
  
  @media (pointer: fine) {
    .btnLinkCta:hover .btnRipple span {
      border-radius: 0;
      transform: translateY(0);
      transition-duration: 0.5s, 0.9s;
    }
  }
  
  @media (min-width: 768px), (orientation: landscape) {
    .btnBorder {
      border-width: 1px;
    }
  }
  
  @media (pointer: fine) {
    .btnLinkCta:hover {
      transform: scaleX(1.02);
      transition: transform 0.6s cubic-bezier(0.34, 5.56, 0.64, 1);
    }
  }
  @media (min-width: 768px), (orientation: landscape) {
    .btnLinkCta {
      // font-size: 2.4rem;
    }
  }
  